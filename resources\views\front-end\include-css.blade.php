<link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&display=swap"
    rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Asap:ital,wght@0,100..900;1,100..900&display=swap"
    rel="stylesheet">
<link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" rel="stylesheet" />

<script src="{{ asset('assets/front-end/assets/js/loopple/kit.fontawesome.js') }}" crossorigin="anonymous"></script>

<link href="{{ asset('assets/front-end/assets/css/nucleo-icons.css') }}" rel="stylesheet" />
<link href="{{ asset('assets/front-end/assets/css/nucleo-svg.css') }}" rel="stylesheet" />
{{--
<link rel="stylesheet" href="{{ asset('assets/front-end/assets/css/theme.css') }}"> --}}
@if($active_theme === 'old' || (isset($theme) && $theme === 'old'))
    <!-- Old Theme Styles -->
    <link rel="stylesheet" href="{{ asset('assets/front-end/assets/old/css/theme.css') }}">
@else
    <!-- New Theme Styles (Current) -->
    <link rel="stylesheet" href="{{ asset('assets/front-end/assets/css/theme.css') }}?v={{ time() }}">
@endif
<link rel="stylesheet" href="{{ asset('assets/front-end/assets/css/loopple/loopple.css') }}">
<link rel="stylesheet" href="{{ asset('assets/front-end/assets/css/custom.css') }}?v={{ time() }}">

<!-- MOBILE RESPONSIVE FIXES - DIRECT IMPLEMENTATION -->
<style>
/* MOBILE ONLY STYLES - FORCE OVERRIDE */
@media screen and (max-width: 768px) {
    /* MOBILE STICKY HEADER - SIMPLIFIED APPROACH */
    nav.navbar, .navbar, #mainNavbar {
        /* JavaScript will handle all positioning and transitions */
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        will-change: transform, position, background, box-shadow !important;
        padding: 0.75rem 0 !important;
        margin: 0 !important;
    }

    /* Ensure navbar container spans full width */
    .navbar .container,
    .navbar .container-fluid {
        width: 100% !important;
        max-width: 100% !important;
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }

    /* Ensure navbar brand and navigation items are properly positioned */
    .navbar-brand,
    .navbar-nav,
    .navbar-toggler {
        position: relative !important;
        z-index: 10001 !important;
    }

    /* ANIMATION KEYFRAMES FOR STICKY TRANSITION */
    @keyframes stickySlideIn {
        0% {
            transform: translateY(-10px) !important;
            opacity: 0.8 !important;
        }
        100% {
            transform: translateY(0) !important;
            opacity: 1 !important;
        }
    }

    /* PRICING PLAN BUTTON BASE STYLES - ALL DEVICES */
    .pricing-button-green {
        background: linear-gradient(135deg, #52C41A 0%, #73D13D 100%) !important;
        border-color: #52C41A !important;
        color: #ffffff !important;
        font-weight: 500 !important;
        transition: all 0.3s ease !important;
    }

    .pricing-button-green:hover {
        background: linear-gradient(135deg, #389E0D 0%, #52C41A 100%) !important;
        border-color: #389E0D !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(82, 196, 26, 0.4) !important;
    }

    .pricing-button-orange {
        background: linear-gradient(135deg, #FF8C00 0%, #FFA940 100%) !important;
        border-color: #FF8C00 !important;
        color: #ffffff !important;
        font-weight: 500 !important;
        transition: all 0.3s ease !important;
    }

    .pricing-button-orange:hover {
        background: linear-gradient(135deg, #D46B08 0%, #FF8C00 100%) !important;
        border-color: #D46B08 !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(255, 140, 0, 0.4) !important;
    }

    .pricing-button-blue {
        background: linear-gradient(135deg, #1890FF 0%, #40A9FF 100%) !important;
        border-color: #1890FF !important;
        color: #ffffff !important;
        font-weight: 500 !important;
        transition: all 0.3s ease !important;
    }

    .pricing-button-blue:hover {
        background: linear-gradient(135deg, #096DD9 0%, #1890FF 100%) !important;
        border-color: #096DD9 !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(24, 144, 255, 0.4) !important;
    }

    /* AUTHENTICATION FORM SUBMIT BUTTONS - ALL DEVICES */

    /* LOGIN FORM SUBMIT BUTTON - BLUE */
    .auth-login-form .btn[type="submit"],
    .login-form .btn[type="submit"],
    form[action*="login"] .btn[type="submit"],
    form[action*="authenticate"] .btn[type="submit"],
    .card-body form .btn[type="submit"]:not(.register-btn),
    #loginForm .btn[type="submit"],
    .login-card .btn[type="submit"],
    .auth-form .btn[type="submit"]:not(.register-btn) {
        background: linear-gradient(135deg, #1890FF 0%, #40A9FF 100%) !important;
        border-color: #1890FF !important;
        color: #ffffff !important;
        font-weight: 500 !important;
        transition: all 0.3s ease !important;
        padding: 0.75rem 1.5rem !important;
        border-radius: 0.375rem !important;
    }

    .auth-login-form .btn[type="submit"]:hover,
    .login-form .btn[type="submit"]:hover,
    form[action*="login"] .btn[type="submit"]:hover,
    form[action*="authenticate"] .btn[type="submit"]:hover,
    .card-body form .btn[type="submit"]:not(.register-btn):hover,
    #loginForm .btn[type="submit"]:hover,
    .login-card .btn[type="submit"]:hover,
    .auth-form .btn[type="submit"]:not(.register-btn):hover {
        background: linear-gradient(135deg, #096DD9 0%, #1890FF 100%) !important;
        border-color: #096DD9 !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(24, 144, 255, 0.4) !important;
    }

    /* REGISTRATION FORM SUBMIT BUTTON - RED */
    .auth-register-form .btn[type="submit"],
    .register-form .btn[type="submit"],
    form[action*="register"] .btn[type="submit"],
    form[action*="signup"] .btn[type="submit"],
    .register-btn,
    #registerForm .btn[type="submit"],
    .register-card .btn[type="submit"],
    .signup-form .btn[type="submit"] {
        background: linear-gradient(135deg, #FF4D4F 0%, #FF7875 100%) !important;
        border-color: #FF4D4F !important;
        color: #ffffff !important;
        font-weight: 500 !important;
        transition: all 0.3s ease !important;
        padding: 0.75rem 1.5rem !important;
        border-radius: 0.375rem !important;
    }

    .auth-register-form .btn[type="submit"]:hover,
    .register-form .btn[type="submit"]:hover,
    form[action*="register"] .btn[type="submit"]:hover,
    form[action*="signup"] .btn[type="submit"]:hover,
    .register-btn:hover,
    #registerForm .btn[type="submit"]:hover,
    .register-card .btn[type="submit"]:hover,
    .signup-form .btn[type="submit"]:hover {
        background: linear-gradient(135deg, #D9363E 0%, #FF4D4F 100%) !important;
        border-color: #D9363E !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(255, 77, 79, 0.4) !important;
    }

    /* HIDE FLOATING HERO IMAGES */
    .floating-card.top-right,
    .floating-card.bottom-left,
    .floating-card,
    img[src*="home_hero1.png"],
    img[src*="home_hero2.png"],
    img[src*="assets/front-end/img/gallery/home_hero1.png"],
    img[src*="assets/front-end/img/gallery/home_hero2.png"] {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
    }

    /* HIDE TO TOP BUTTON */
    #backToTopBtn,
    .back-to-top,
    [id*="backToTop"],
    [class*="back-to-top"] {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
    }

    /* REDUCE HERO SECTION SPACING */
    .hero-images-section,
    .carousel-section,
    section[class*="hero"] {
        padding-top: 1rem !important;
        padding-bottom: 0.5rem !important;
        margin-top: 0 !important;
        margin-bottom: 0.5rem !important;
    }

    .main-title,
    h1.main-title,
    .hero-images-section h1,
    .carousel-section h1 {
        margin-bottom: 1rem !important;
        font-size: 1.8rem !important;
        padding: 0 1rem !important;
    }

    /* MOBILE IMAGE OPTIMIZATION - ENHANCED SIZE AND POSITION */
    .main-image-container {
        padding: 0 0.5rem !important;
        max-width: 100% !important;
        margin-top: 38px !important; /* Move image 1cm (38px) downward */
        transform: translateY(38px) !important; /* Additional downward positioning */
    }

    .main-image-container img {
        width: calc(100% + 155px) !important; /* Increase width by 4.1cm total (79px + 76px for 2cm more) */
        height: auto !important;
        min-height: calc(100% + 291px) !important; /* Increase height by 7.7cm total (159px + 132px for 3.5cm more) */
        border-radius: 0.75rem !important;
        max-width: none !important; /* Allow image to exceed container width */
        margin-left: -77.5px !important; /* Center the enlarged image (155px / 2) */
        object-fit: cover !important; /* Maintain aspect ratio while filling space */
    }


}

/* FORCE MOBILE DETECTION */
@media screen and (max-width: 767px) {
    /* Additional mobile targeting */
    .navbar-brand,
    .navbar-nav,
    .navbar-toggler {
        position: relative !important;
        z-index: 10000 !important;
    }
}
</style>

<!-- MOBILE STICKY HEADER JAVASCRIPT - FIXED VERSION -->
<script>
document.addEventListener('DOMContentLoaded', function() {

    function isMobile() {
        return window.innerWidth <= 768;
    }

    function initMobileStickyHeader() {
        if (!isMobile()) return;

        const navbar = document.querySelector('.navbar') || document.querySelector('#mainNavbar') || document.querySelector('nav.navbar');
        const body = document.body;

        if (!navbar) return;

        let isSticky = false;
        let navbarHeight = 0;
        let triggerPoint = 100; // Scroll 100px to trigger sticky

        // Force initial state
        navbar.style.cssText = `
            position: relative !important;
            top: auto !important;
            background: #ffffff !important;
            box-shadow: none !important;
            backdrop-filter: none !important;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        `;

        function measureNavbar() {
            navbarHeight = navbar.offsetHeight || 70;
        }

        function makeSticky() {
            if (isSticky) return;
            isSticky = true;

            // Add padding to body to prevent jump
            body.style.paddingTop = navbarHeight + 'px';
            body.style.transition = 'padding-top 0.3s ease';

            // Make navbar sticky with immediate fixed position
            navbar.style.cssText = `
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                width: 100vw !important;
                z-index: 9999 !important;
                background: rgba(255, 255, 255, 0.98) !important;
                backdrop-filter: blur(20px) !important;
                -webkit-backdrop-filter: blur(20px) !important;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
                transform: translateY(0) !important;
                transition: none !important;
                padding: 0.75rem 0 !important;
                margin: 0 !important;
            `;

            // Add smooth entrance animation
            navbar.style.animation = 'stickySlideIn 0.3s ease-out forwards';
        }

        function makeNormal() {
            if (!isSticky) return;
            isSticky = false;

            // Remove body padding
            body.style.paddingTop = '0px';

            // Return to normal position
            navbar.style.cssText = `
                position: relative !important;
                top: auto !important;
                left: auto !important;
                right: auto !important;
                width: 100% !important;
                z-index: 1030 !important;
                background: #ffffff !important;
                backdrop-filter: none !important;
                box-shadow: none !important;
                transform: translateY(0) !important;
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
            `;
        }

        function handleScroll() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

            if (scrollTop > triggerPoint && !isSticky) {
                makeSticky();
            } else if (scrollTop <= (triggerPoint - 20) && isSticky) {
                // Add small buffer to prevent flickering
                makeNormal();
            }
        }

        // Debounced scroll handler for stability
        let scrollTimeout;
        function onScroll() {
            if (isMobile()) {
                clearTimeout(scrollTimeout);
                scrollTimeout = setTimeout(handleScroll, 10);
            }
        }

        // Initialize
        measureNavbar();

        // Attach listeners
        window.addEventListener('scroll', onScroll, { passive: true });

        // Handle resize
        window.addEventListener('resize', () => {
            if (isMobile()) {
                measureNavbar();
                handleScroll();
            } else {
                // Reset on desktop
                makeNormal();
            }
        });
    }

    // Initialize mobile sticky header
    initMobileStickyHeader();

    // Re-initialize on resize if switching to mobile
    window.addEventListener('resize', () => {
        setTimeout(initMobileStickyHeader, 100);
    });
});
</script>

<!-- PRICING PLAN BUTTON COLORS - ALL DEVICES JAVASCRIPT -->
<script>
document.addEventListener('DOMContentLoaded', function() {

    function applyPricingButtonColors() {
        console.log('🎨 Applying pricing button colors...');

        // SUPER AGGRESSIVE APPROACH - Find ALL possible pricing containers
        const allPossibleSelectors = [
            '#monthly', '#annual', '#lifetime',
            '.tab-pane', '.tab-content', '.pricing-section', '.pricing-plans',
            '[id*="monthly"]', '[id*="annual"]', '[id*="lifetime"]',
            '[class*="pricing"]', '[class*="plan"]', '[class*="subscription"]'
        ];

        allPossibleSelectors.forEach(selector => {
            const containers = document.querySelectorAll(selector);

            containers.forEach(container => {
                if (!container) return;

                // Find ALL possible card selectors
                const cardSelectors = [
                    '.pricing-card', '.card', '.plan-card', '.price-card',
                    '.col-md-4', '.col-lg-4', '.col-sm-6', '.col-12',
                    '[class*="card"]', '[class*="plan"]', '[class*="pricing"]'
                ];

                cardSelectors.forEach(cardSelector => {
                    const cards = container.querySelectorAll(cardSelector);

                    cards.forEach((card, index) => {
                        // Find ALL possible button selectors
                        const buttonSelectors = [
                            '.btn', '.btn-primary', '.btn-secondary', '.button',
                            '[class*="btn"]', 'a[href*="checkout"]', 'button',
                            '.buy-now', '.purchase', '.select-plan'
                        ];

                        buttonSelectors.forEach(btnSelector => {
                            const buttons = card.querySelectorAll(btnSelector);

                            buttons.forEach(button => {
                                // Skip if already processed
                                if (button.dataset.colorProcessed) return;
                                button.dataset.colorProcessed = 'true';

                                // Remove any existing classes
                                button.classList.remove('pricing-button-green', 'pricing-button-orange', 'pricing-button-blue');

                                // FORCE APPLY COLORS
                                if (index === 0) {
                                    // 1st card = GREEN
                                    button.classList.add('pricing-button-green');
                                    button.style.cssText = `
                                        background: linear-gradient(135deg, #52C41A 0%, #73D13D 100%) !important;
                                        border-color: #52C41A !important;
                                        color: #ffffff !important;
                                        font-weight: 500 !important;
                                        transition: all 0.3s ease !important;
                                    `;
                                    console.log('✅ Applied GREEN to card', index + 1);
                                } else if (index === 1) {
                                    // 2nd card = ORANGE
                                    button.classList.add('pricing-button-orange');
                                    button.style.cssText = `
                                        background: linear-gradient(135deg, #FF8C00 0%, #FFA940 100%) !important;
                                        border-color: #FF8C00 !important;
                                        color: #ffffff !important;
                                        font-weight: 500 !important;
                                        transition: all 0.3s ease !important;
                                    `;
                                    console.log('✅ Applied ORANGE to card', index + 1);
                                } else if (index === 2) {
                                    // 3rd card = BLUE
                                    button.classList.add('pricing-button-blue');
                                    button.style.cssText = `
                                        background: linear-gradient(135deg, #1890FF 0%, #40A9FF 100%) !important;
                                        border-color: #1890FF !important;
                                        color: #ffffff !important;
                                        font-weight: 500 !important;
                                        transition: all 0.3s ease !important;
                                    `;
                                    console.log('✅ Applied BLUE to card', index + 1);
                                }

                                // Add hover effects
                                button.addEventListener('mouseenter', function() {
                                    if (this.classList.contains('pricing-button-green')) {
                                        this.style.cssText += `
                                            background: linear-gradient(135deg, #389E0D 0%, #52C41A 100%) !important;
                                            transform: translateY(-2px) !important;
                                            box-shadow: 0 8px 25px rgba(82, 196, 26, 0.4) !important;
                                        `;
                                    } else if (this.classList.contains('pricing-button-orange')) {
                                        this.style.cssText += `
                                            background: linear-gradient(135deg, #D46B08 0%, #FF8C00 100%) !important;
                                            transform: translateY(-2px) !important;
                                            box-shadow: 0 8px 25px rgba(255, 140, 0, 0.4) !important;
                                        `;
                                    } else if (this.classList.contains('pricing-button-blue')) {
                                        this.style.cssText += `
                                            background: linear-gradient(135deg, #096DD9 0%, #1890FF 100%) !important;
                                            transform: translateY(-2px) !important;
                                            box-shadow: 0 8px 25px rgba(24, 144, 255, 0.4) !important;
                                        `;
                                    }
                                });

                                button.addEventListener('mouseleave', function() {
                                    // Reset to original colors
                                    if (this.classList.contains('pricing-button-green')) {
                                        this.style.cssText = `
                                            background: linear-gradient(135deg, #52C41A 0%, #73D13D 100%) !important;
                                            border-color: #52C41A !important;
                                            color: #ffffff !important;
                                            font-weight: 500 !important;
                                            transition: all 0.3s ease !important;
                                            transform: translateY(0) !important;
                                            box-shadow: none !important;
                                        `;
                                    } else if (this.classList.contains('pricing-button-orange')) {
                                        this.style.cssText = `
                                            background: linear-gradient(135deg, #FF8C00 0%, #FFA940 100%) !important;
                                            border-color: #FF8C00 !important;
                                            color: #ffffff !important;
                                            font-weight: 500 !important;
                                            transition: all 0.3s ease !important;
                                            transform: translateY(0) !important;
                                            box-shadow: none !important;
                                        `;
                                    } else if (this.classList.contains('pricing-button-blue')) {
                                        this.style.cssText = `
                                            background: linear-gradient(135deg, #1890FF 0%, #40A9FF 100%) !important;
                                            border-color: #1890FF !important;
                                            color: #ffffff !important;
                                            font-weight: 500 !important;
                                            transition: all 0.3s ease !important;
                                            transform: translateY(0) !important;
                                            box-shadow: none !important;
                                        `;
                                    }
                                });
                            });
                        });
                    });
                });
            });
        });

        // Also try alternative selectors for different HTML structures
        const allPricingContainers = document.querySelectorAll('.pricing, .plans, .subscription, [class*="price"]');

        allPricingContainers.forEach(container => {
            const cards = container.querySelectorAll('.card, .pricing-card, .plan, [class*="card"]');

            cards.forEach((card, index) => {
                const buttons = card.querySelectorAll('.btn, .btn-primary, .button, [class*="btn"]');

                buttons.forEach(button => {
                    // Skip if already has color class
                    if (button.classList.contains('pricing-button-green') ||
                        button.classList.contains('pricing-button-orange') ||
                        button.classList.contains('pricing-button-blue')) {
                        return;
                    }

                    // Apply colors based on position
                    if (index === 0) {
                        button.classList.add('pricing-button-green');
                    } else if (index === 1) {
                        button.classList.add('pricing-button-orange');
                    } else if (index === 2) {
                        button.classList.add('pricing-button-blue');
                    }
                });
            });
        });

        console.log('Pricing button colors applied to all devices');
    }

    // Apply colors immediately
    applyPricingButtonColors();

    // SUPER AGGRESSIVE TAB SWITCHING DETECTION
    const allPossibleTabSelectors = [
        '[data-bs-toggle="tab"]', '[data-toggle="tab"]', '.nav-link',
        '[href="#monthly"]', '[href="#annual"]', '[href="#lifetime"]',
        '.nav-item', '.tab-link', '[class*="tab"]', '[class*="nav"]'
    ];

    allPossibleTabSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            element.addEventListener('click', function() {
                console.log('🔄 Tab clicked, reapplying colors...');
                setTimeout(applyPricingButtonColors, 50);
                setTimeout(applyPricingButtonColors, 200);
                setTimeout(applyPricingButtonColors, 500);
            });
        });
    });

    // AGGRESSIVE INTERVAL CHECKING (for stubborn cases)
    let intervalCount = 0;
    const aggressiveInterval = setInterval(() => {
        applyPricingButtonColors();
        intervalCount++;

        // Stop after 20 attempts (10 seconds)
        if (intervalCount >= 20) {
            clearInterval(aggressiveInterval);
            console.log('🛑 Stopped aggressive interval checking');
        }
    }, 500);

    // Re-apply on window resize
    window.addEventListener('resize', function() {
        setTimeout(applyPricingButtonColors, 100);
    });

    // Enhanced MutationObserver
    const observer = new MutationObserver(function(mutations) {
        let shouldReapply = false;

        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                shouldReapply = true;
            }
            if (mutation.type === 'attributes' &&
                (mutation.attributeName === 'class' || mutation.attributeName === 'style')) {
                shouldReapply = true;
            }
        });

        if (shouldReapply) {
            setTimeout(applyPricingButtonColors, 100);
        }
    });

    // Start observing with more options
    observer.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['class', 'style']
    });

    // Force reapply every 2 seconds for first 10 seconds
    setTimeout(() => applyPricingButtonColors(), 2000);
    setTimeout(() => applyPricingButtonColors(), 4000);
    setTimeout(() => applyPricingButtonColors(), 6000);
    setTimeout(() => applyPricingButtonColors(), 8000);
    setTimeout(() => applyPricingButtonColors(), 10000);

    // AUTHENTICATION FORM BUTTON COLORS - FORCE APPLY
    function applyAuthButtonColors() {
        console.log('🔐 Applying authentication button colors...');

        // LOGIN BUTTON SELECTORS - BLUE
        const loginSelectors = [
            '.auth-login-form .btn[type="submit"]',
            '.login-form .btn[type="submit"]',
            'form[action*="login"] .btn[type="submit"]',
            'form[action*="authenticate"] .btn[type="submit"]',
            '#loginForm .btn[type="submit"]',
            '.login-card .btn[type="submit"]',
            '.card-body form .btn[type="submit"]'
        ];

        loginSelectors.forEach(selector => {
            const buttons = document.querySelectorAll(selector);
            buttons.forEach(button => {
                // Skip if it's a register button
                if (button.classList.contains('register-btn') ||
                    button.textContent.toLowerCase().includes('register') ||
                    button.textContent.toLowerCase().includes('sign up')) {
                    return;
                }

                // Force apply BLUE for login
                button.style.cssText = `
                    background: linear-gradient(135deg, #1890FF 0%, #40A9FF 100%) !important;
                    border-color: #1890FF !important;
                    color: #ffffff !important;
                    font-weight: 500 !important;
                    transition: all 0.3s ease !important;
                    padding: 0.75rem 1.5rem !important;
                    border-radius: 0.375rem !important;
                `;

                // Add hover effect for login
                button.addEventListener('mouseenter', function() {
                    this.style.cssText += `
                        background: linear-gradient(135deg, #096DD9 0%, #1890FF 100%) !important;
                        transform: translateY(-2px) !important;
                        box-shadow: 0 8px 25px rgba(24, 144, 255, 0.4) !important;
                    `;
                });

                button.addEventListener('mouseleave', function() {
                    this.style.cssText = `
                        background: linear-gradient(135deg, #1890FF 0%, #40A9FF 100%) !important;
                        border-color: #1890FF !important;
                        color: #ffffff !important;
                        font-weight: 500 !important;
                        transition: all 0.3s ease !important;
                        padding: 0.75rem 1.5rem !important;
                        border-radius: 0.375rem !important;
                        transform: translateY(0) !important;
                        box-shadow: none !important;
                    `;
                });

                console.log('✅ Applied BLUE to login button');
            });
        });

        // REGISTER BUTTON SELECTORS - RED
        const registerSelectors = [
            '.auth-register-form .btn[type="submit"]',
            '.register-form .btn[type="submit"]',
            'form[action*="register"] .btn[type="submit"]',
            'form[action*="signup"] .btn[type="submit"]',
            '.register-btn',
            '#registerForm .btn[type="submit"]',
            '.register-card .btn[type="submit"]',
            '.signup-form .btn[type="submit"]'
        ];

        registerSelectors.forEach(selector => {
            const buttons = document.querySelectorAll(selector);
            buttons.forEach(button => {
                // Force apply RED for register
                button.style.cssText = `
                    background: linear-gradient(135deg, #FF4D4F 0%, #FF7875 100%) !important;
                    border-color: #FF4D4F !important;
                    color: #ffffff !important;
                    font-weight: 500 !important;
                    transition: all 0.3s ease !important;
                    padding: 0.75rem 1.5rem !important;
                    border-radius: 0.375rem !important;
                `;

                // Add hover effect for register
                button.addEventListener('mouseenter', function() {
                    this.style.cssText += `
                        background: linear-gradient(135deg, #D9363E 0%, #FF4D4F 100%) !important;
                        transform: translateY(-2px) !important;
                        box-shadow: 0 8px 25px rgba(255, 77, 79, 0.4) !important;
                    `;
                });

                button.addEventListener('mouseleave', function() {
                    this.style.cssText = `
                        background: linear-gradient(135deg, #FF4D4F 0%, #FF7875 100%) !important;
                        border-color: #FF4D4F !important;
                        color: #ffffff !important;
                        font-weight: 500 !important;
                        transition: all 0.3s ease !important;
                        padding: 0.75rem 1.5rem !important;
                        border-radius: 0.375rem !important;
                        transform: translateY(0) !important;
                        box-shadow: none !important;
                    `;
                });

                console.log('✅ Applied RED to register button');
            });
        });

        // Also check for buttons by text content
        const allSubmitButtons = document.querySelectorAll('button[type="submit"], input[type="submit"], .btn[type="submit"]');
        allSubmitButtons.forEach(button => {
            const buttonText = button.textContent || button.value || '';

            if (buttonText.toLowerCase().includes('login') ||
                buttonText.toLowerCase().includes('sign in') ||
                buttonText.toLowerCase().includes('log in')) {

                // Apply BLUE for login
                button.style.cssText = `
                    background: linear-gradient(135deg, #1890FF 0%, #40A9FF 100%) !important;
                    border-color: #1890FF !important;
                    color: #ffffff !important;
                    font-weight: 500 !important;
                    transition: all 0.3s ease !important;
                    padding: 0.75rem 1.5rem !important;
                    border-radius: 0.375rem !important;
                `;
                console.log('✅ Applied BLUE to login button by text');

            } else if (buttonText.toLowerCase().includes('register') ||
                       buttonText.toLowerCase().includes('sign up') ||
                       buttonText.toLowerCase().includes('signup') ||
                       buttonText.toLowerCase().includes('create account')) {

                // Apply RED for register
                button.style.cssText = `
                    background: linear-gradient(135deg, #FF4D4F 0%, #FF7875 100%) !important;
                    border-color: #FF4D4F !important;
                    color: #ffffff !important;
                    font-weight: 500 !important;
                    transition: all 0.3s ease !important;
                    padding: 0.75rem 1.5rem !important;
                    border-radius: 0.375rem !important;
                `;
                console.log('✅ Applied RED to register button by text');
            }
        });
    }

    // Apply auth button colors immediately
    applyAuthButtonColors();

    // Reapply auth colors periodically
    setTimeout(() => applyAuthButtonColors(), 1000);
    setTimeout(() => applyAuthButtonColors(), 3000);
    setTimeout(() => applyAuthButtonColors(), 5000);
});
</script>

<!-- Theme Color Overrides - Load Last -->
@if($active_theme !== 'old' && (!isset($theme) || $theme !== 'old'))
<style>
/* Professional Red Theme Overrides - Ensure these load last */
:root {
    --gohub-primary: #D32F2F !important;
    --gohub-secondary: #B71C1C !important;
    --gohub-primary-rgb: 211, 47, 47 !important;
    --gohub-secondary-rgb: 183, 28, 28 !important;
}

.btn-primary, .btn.btn-primary, .btn-btn-primary {
    background-color: #D32F2F !important;
    border-color: #D32F2F !important;
    color: #ffffff !important;
}

.btn-primary:hover, .btn.btn-primary:hover, .btn-btn-primary:hover {
    background-color: #B71C1C !important;
    border-color: #B71C1C !important;
    color: #ffffff !important;
}

.btn-secondary, .btn.btn-secondary {
    background-color: #B71C1C !important;
    border-color: #B71C1C !important;
    color: #ffffff !important;
}

.btn-secondary:hover, .btn.btn-secondary:hover {
    background-color: #8D1515 !important;
    border-color: #8D1515 !important;
    color: #ffffff !important;
}

.bg-primary {
    background-color: #D32F2F !important;
}

.bg-secondary {
    background-color: #B71C1C !important;
}

.text-primary {
    color: #D32F2F !important;
}

.text-secondary {
    color: #B71C1C !important;
}

.navbar-brand {
    color: #D32F2F !important;
}

.nav-pills .nav-link.active {
    background-color: #D32F2F !important;
    color: #ffffff !important;
}

/* Theme icon update for modern theme */
.theme-modern .theme-icon {
    background: linear-gradient(135deg, #D32F2F 0%, #B71C1C 100%) !important;
}

/* Specific Homepage and Header Button Styling */
/* Get Started button on homepage */
.btn-gradient-dark {
    background: linear-gradient(135deg, #D32F2F 0%, #B71C1C 100%) !important;
    border: none !important;
    color: #ffffff !important;
}

.btn-gradient-dark:hover {
    background: linear-gradient(135deg, #B71C1C 0%, #8D1515 100%) !important;
    color: #ffffff !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(211, 47, 47, 0.3) !important;
}

/* Login button in header - only this button, not the whole header */
.btn-outline-login {
    background-color: transparent !important;
    border: 2px solid #D32F2F !important;
    color: #D32F2F !important;
}

.btn-outline-login:hover {
    background-color: #D32F2F !important;
    border-color: #D32F2F !important;
    color: #ffffff !important;
}

/* Ensure header background stays normal (not red) */
.navbar.bg-primary {
    background-color: #ffffff !important;
    background: #ffffff !important;
}

/* Keep navbar brand and other elements normal */
.navbar-brand {
    color: #333333 !important;
}

.nav-link {
    color: #333333 !important;
}

.nav-link:hover {
    color: #D32F2F !important;
}

/* Override for pricing tabs specifically */
ul.pricing-tabs .nav-link {
    color: #333333 !important;
}

ul.pricing-tabs .nav-link:hover {
    color: #000000 !important;
}

/* Ensure all red buttons have white text for visibility */
.btn-plan-1, .btn-plan-2, .btn-plan-3, .btn-plan-4, .btn-plan-5,
.btn-outline-plan-1, .btn-outline-plan-2, .btn-outline-plan-3, .btn-outline-plan-4, .btn-outline-plan-5 {
    color: #ffffff !important;
}

.btn-outline-plan-1:hover, .btn-outline-plan-2:hover, .btn-outline-plan-3:hover, .btn-outline-plan-4:hover, .btn-outline-plan-5:hover {
    color: #ffffff !important;
}

/* Make outline plan buttons (free plans) have same background as solid plan buttons */
.btn-outline-plan-1, .btn-outline-plan-2, .btn-outline-plan-3, .btn-outline-plan-4, .btn-outline-plan-5 {
    background-color: #28a745 !important;
    border-color: #28a745 !important;
    color: #ffffff !important;
}

.btn-outline-plan-1:hover, .btn-outline-plan-2:hover, .btn-outline-plan-3:hover, .btn-outline-plan-4:hover, .btn-outline-plan-5:hover {
    background-color: #218838 !important;
    border-color: #218838 !important;
    color: #ffffff !important;
}

/* Additional red button classes that might need white text */
.bg-gradient-dark, .btn.bg-gradient-dark {
    color: #ffffff !important;
}

/* Ensure any button with red background has white text */
[class*="btn-plan-"], [class*="btn-outline-plan-"] {
    color: #ffffff !important;
}

/* Monthly, Annual, Lifetime tab buttons styling - More specific selectors */
ul.pricing-tabs.nav-pills .nav-link {
    color: #333333 !important; /* Default text color */
}

ul.pricing-tabs.nav-pills .nav-link:hover {
    color: #000000 !important; /* Black text on hover */
}

ul.pricing-tabs.nav-pills .nav-link.active,
ul.pricing-tabs.nav-pills .nav-link.active:focus {
    background-color: #D32F2F !important; /* Red background when selected */
    color: #ffffff !important; /* White text when selected */
    border-color: #D32F2F !important;
}

ul.pricing-tabs.nav-pills .nav-link.active:hover {
    background-color: #D32F2F !important;
    color: #ffffff !important; /* Keep white text on hover when active */
}

/* Additional Get Started buttons throughout the site */
.btn-primary.btn-lg {
    background-color: #D32F2F !important;
    border-color: #D32F2F !important;
    color: #ffffff !important;
}

.btn-primary.btn-lg:hover {
    background-color: #B71C1C !important;
    border-color: #B71C1C !important;
    color: #ffffff !important;
}

/* Footer Copyright Section - Black background with white text */
.footer .row.text-center:last-child {
    background-color: #000000 !important;
    color: #ffffff !important;
    padding: 15px 20px !important;
    margin: 0 -15px !important;
    border-radius: 8px !important;
}

.footer .row.text-center:last-child p {
    color: #ffffff !important;
    margin-bottom: 0 !important;
}

.footer .row.text-center:last-child .col-md-auto {
    color: #ffffff !important;
}

/* Ensure any links in footer text are white */
.footer .row.text-center:last-child a {
    color: #ffffff !important;
    text-decoration: underline !important;
}

.footer .row.text-center:last-child a:hover {
    color: #cccccc !important;
}

/* Login page - Main heading styling only */
/* Target only the "Login or Register" heading, not the subheading */
.section-py .container h1.display-5.fw-semi-bold.mt-5.text-center {
    color: #007bff !important; /* Blue color */
    font-weight: bold !important; /* Bold text */
}

/* ===== NESTKO CHATBOT WIDGET STYLES ===== */
.nestko-chat-widget {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* ===== MODERN WELCOME SCREEN STYLES ===== */
.modern-welcome-screen {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    background: #ffffff;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    /* Ensure exact same dimensions as chat screen */
    max-width: 380px;
    max-height: 600px;
    min-width: 320px;
    min-height: 480px;
}

.welcome-header-gradient {
    background: linear-gradient(135deg, #D32F2F 0%, #B71C1C 100%);
    padding: 24px 20px 32px 20px;
    position: relative;
    text-align: center;
}

.close-btn-modern {
    position: absolute;
    top: 16px;
    right: 16px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 8px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
}

.close-btn-modern:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.chat-icon-modern {
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px auto;
    color: white;
}

.welcome-title-modern {
    font-size: 24px;
    font-weight: 700;
    color: white;
    margin: 0 0 12px 0;
    line-height: 1.3;
    letter-spacing: -0.5px;
}

.wave-emoji {
    font-size: 32px;
    margin-top: 8px;
    animation: wave 2s ease-in-out infinite;
}

@keyframes wave {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(20deg); }
    75% { transform: rotate(-10deg); }
}

.welcome-content-modern {
    flex: 1;
    padding: 24px 20px 20px 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.chatbot-info-card {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #e9ecef;
}

.chatbot-avatar-modern {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #4285F4;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.chatbot-info-text {
    flex: 1;
}

.chatbot-name {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 2px;
}

.chatbot-subtitle {
    font-size: 14px;
    color: #6b7280;
    line-height: 1.4;
}

.chat-btn-modern {
    background: linear-gradient(135deg, #D32F2F 0%, #B71C1C 100%);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 16px 24px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 12px rgba(211, 47, 47, 0.3);
    position: relative;
    overflow: hidden;
}

.chat-btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(211, 47, 47, 0.4);
}

.chat-btn-modern:active {
    transform: translateY(0);
}

.action-items-modern {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.action-item {
    display: flex;
    align-items: center;
    padding: 14px 16px;
    background: #f8f9fa;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid #e9ecef;
}

.action-item:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

.action-icon {
    font-size: 16px;
    margin-right: 12px;
}

.action-text {
    flex: 1;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
}

.action-arrow {
    color: #9ca3af;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.action-item:hover .action-arrow {
    background: #D32F2F;
    color: white;
    transform: translateX(2px);
}

.bottom-nav-modern {
    display: flex;
    background: #f8f9fa;
    border-radius: 12px;
    padding: 8px;
    gap: 4px;
    margin-top: auto;
}

.nav-item-modern {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px 8px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #6b7280;
    font-size: 12px;
    font-weight: 500;
}

.nav-item-modern.active {
    background: white;
    color: #D32F2F;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.nav-item-modern svg {
    margin-bottom: 4px;
}

.powered-by-modern {
    text-align: center;
    font-size: 12px;
    color: #9ca3af;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e9ecef;
}

/* ===== RED THEME CHAT STYLES ===== */
.reference-chat-screen {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, #FFF5F5 0%, #FEF2F2 100%);
    border-radius: 16px;
    overflow: hidden;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    /* Ensure exact same dimensions as welcome screen */
    max-width: 380px;
    max-height: 600px;
    min-width: 320px;
    min-height: 480px;
    border: 2px solid #FEE2E2;
}

.reference-chat-header {
    background: linear-gradient(135deg, #D32F2F 0%, #B71C1C 100%);
    padding: 12px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #FFCDD2;
    min-height: 56px;
    box-shadow: 0 2px 8px rgba(211, 47, 47, 0.2);
}

.header-left-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.back-arrow-btn,
.three-dots-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    padding: 8px;
    border-radius: 8px;
    cursor: pointer;
    color: white;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.back-arrow-btn:hover,
.three-dots-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
    transform: scale(1.05);
}

.header-center-section {
    flex: 1;
    display: flex;
    justify-content: center;
}

.ai-assistant-badge {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.95);
    padding: 6px 12px;
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ai-avatar-circle {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #D32F2F;
    transition: all 0.3s ease;
}

.ai-avatar-circle.typing {
    animation: aiPulse 1.5s ease-in-out infinite;
}

@keyframes aiPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(211, 47, 47, 0.7);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 0 8px rgba(211, 47, 47, 0);
    }
}

.ai-assistant-text {
    font-size: 14px;
    font-weight: 600;
    color: #D32F2F;
}

.header-right-section {
    display: flex;
    align-items: center;
    gap: 8px;
}

.minimize-btn,
.close-x-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    padding: 8px;
    border-radius: 8px;
    cursor: pointer;
    color: white;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.minimize-btn:hover,
.close-x-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
    transform: scale(1.05);
}

.reference-messages-area {
    flex: 1;
    padding: 20px 16px;
    overflow-y: auto;
    background: #F5F5F5;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.message-with-avatar-ref {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 16px;
}

.message-avatar-ref {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #8B5CF6;
    flex-shrink: 0;
}

.message-content-ref {
    flex: 1;
}

.message-text-ref {
    background: #FFFFFF;
    color: #374151;
    padding: 12px 16px;
    border-radius: 18px;
    font-size: 14px;
    line-height: 1.5;
    max-width: 280px;
    border: 1px solid #E5E7EB;
    position: relative;
}

.message-text-ref::before {
    content: '';
    position: absolute;
    left: -8px;
    top: 12px;
    width: 0;
    height: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-right: 8px solid #FFFFFF;
}

.assistant-message-ref {
    background: #FFFFFF;
    color: #374151;
    padding: 12px 16px;
    border-radius: 18px;
    font-size: 14px;
    line-height: 1.5;
    max-width: 280px;
    border: 1px solid #E5E7EB;
    margin-left: 44px;
    margin-bottom: 16px;
}

.action-buttons-grid-ref {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-left: 44px;
    max-width: 280px;
}

.action-btn-ref {
    background: #FFFFFF;
    border: 2px solid #FFCDD2;
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 13px;
    color: #D32F2F;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(211, 47, 47, 0.1);
}

.action-btn-ref:hover {
    background: linear-gradient(135deg, #D32F2F 0%, #B71C1C 100%);
    border-color: #D32F2F;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(211, 47, 47, 0.3);
}

.action-btn-ref.centered-btn {
    width: 100%;
    text-align: center;
    margin-top: 4px;
}

.reference-input-area {
    background: #FFFFFF;
    padding: 16px;
    border-top: 1px solid #E5E7EB;
}

.input-wrapper-ref {
    display: flex;
    align-items: center;
    background: #FFFFFF;
    border: 2px solid #FFCDD2;
    border-radius: 24px;
    padding: 8px 12px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(211, 47, 47, 0.1);
}

.input-wrapper-ref:focus-within {
    border-color: #D32F2F;
    box-shadow: 0 0 0 3px rgba(211, 47, 47, 0.1);
}

.message-input-ref {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    font-size: 14px;
    color: #374151;
    padding: 8px 4px;
}

.message-input-ref::placeholder {
    color: #9CA3AF;
}

.input-actions-ref {
    display: flex;
    align-items: center;
    gap: 8px;
}

.emoji-btn-ref,
.ai-rephrase-btn-ref {
    background: none;
    border: none;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    color: #D32F2F;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.emoji-btn-ref:hover,
.ai-rephrase-btn-ref:hover {
    background: #FFEBEE;
    color: #B71C1C;
    transform: scale(1.1);
}

.ai-rephrase-btn-ref.processing {
    animation: aiSpin 1s linear infinite;
}

@keyframes aiSpin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Emoji Picker Styles */
.emoji-picker-ref {
    position: absolute;
    bottom: 70px;
    left: 16px;
    width: 133px; /* 3.5cm */
    height: 133px; /* 3.5cm */
    background: white;
    border: 2px solid #FFCDD2;
    border-radius: 12px;
    padding: 8px;
    box-shadow: 0 8px 24px rgba(211, 47, 47, 0.2);
    z-index: 1000;
    overflow-y: auto;
    display: none;
}

.emoji-grid-ref {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 4px;
    height: 100%;
}

.emoji-item-ref {
    padding: 4px;
    border-radius: 6px;
    cursor: pointer;
    text-align: center;
    transition: all 0.2s ease;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.emoji-item-ref:hover {
    background: #FFEBEE;
    transform: scale(1.2);
}

/* Mobile responsive emoji picker */
@media (max-width: 480px) {
    .emoji-grid-ref {
        grid-template-columns: repeat(4, 1fr);
    }

    .emoji-item-ref {
        font-size: 14px;
    }
}

.send-btn-ref {
    background: linear-gradient(135deg, #D32F2F 0%, #B71C1C 100%);
    border: none;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    color: white;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(211, 47, 47, 0.3);
}

.send-btn-ref:hover {
    background: linear-gradient(135deg, #B71C1C 0%, #8B0000 100%);
    transform: scale(1.1);
    box-shadow: 0 4px 16px rgba(211, 47, 47, 0.4);
}

.powered-by-ref {
    text-align: center;
    font-size: 12px;
    color: #9CA3AF;
    margin-top: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.nestko-ai-logo {
    display: flex;
    align-items: center;
    gap: 4px;
    font-weight: 600;
    color: #D32F2F;
}

/* Modern Chatbot Widget Icon */
#widget-trigger button {
    background: linear-gradient(135deg, #D32F2F 0%, #B71C1C 100%);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 24px rgba(211, 47, 47, 0.3);
    position: relative;
    overflow: hidden;
}

#widget-trigger button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

#widget-trigger button:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 12px 32px rgba(211, 47, 47, 0.4);
}

#widget-trigger button:hover::before {
    left: 100%;
}

#widget-trigger button:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
}

/* Floating animation for widget */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-6px); }
}

#widget-trigger {
    animation: float 3s ease-in-out infinite;
}

#widget-trigger:hover {
    animation: none;
}

/* Three Dots Menu Styles */
.three-dots-menu {
    background: white;
    border: 2px solid #FFCDD2;
    border-radius: 8px;
    box-shadow: 0 8px 24px rgba(211, 47, 47, 0.2);
    min-width: 150px;
    z-index: 1001;
    overflow: hidden;
    animation: menuSlideIn 0.2s ease-out;
}

@keyframes menuSlideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.menu-item {
    padding: 12px 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #374151;
    transition: all 0.2s ease;
    border-bottom: 1px solid #F3F4F6;
}

.menu-item:last-child {
    border-bottom: none;
}

.menu-item:hover {
    background: #FFEBEE;
    color: #D32F2F;
}

.menu-item svg {
    color: #D32F2F;
}

/* Modern message and typing styles removed as requested */

.chat-bubble-user {
    background: linear-gradient(135deg, #D32F2F 0%, #B71C1C 100%);
    color: white;
    margin-left: 2rem;
    border-radius: 1rem 1rem 0.25rem 1rem;
    max-width: 280px;
    padding: 1rem;
    box-shadow: 0 4px 12px rgba(211, 47, 47, 0.3);
    transform: translateY(0);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chat-bubble-assistant {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    color: #2c3e50;
    margin-right: 2rem;
    border-radius: 1rem 1rem 1rem 0.25rem;
    max-width: 280px;
    padding: 1rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid #e1e8ed;
    transform: translateY(0);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chat-input {
    border-radius: 9999px;
    border: 1px solid #d1d5db;
    background: #f9fafb;
    padding: 0.5rem 1rem;
    padding-right: 3rem;
    width: 100%;
}

.chat-input:focus {
    outline: none;
    ring: 2px;
    ring-color: #3b82f6;
    border-color: transparent;
}

.quick-action-btn {
    background: white;
    border: 1px solid #e5e7eb;
    color: #374151;
    border-radius: 9999px;
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    transition: all 0.2s;
    cursor: pointer;
}

.quick-action-btn:hover {
    background: #f9fafb;
}

/* Enhanced Widget Main Container */
.widget-main {
    width: 400px;
    max-height: 680px;
    background: white;
    border-radius: 1.25rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05);
    display: none;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid #e1e8ed;
    backdrop-filter: blur(10px);
}

/* Enhanced Avatar Containers */
.avatar-container {
    width: 2.25rem;
    height: 2.25rem;
    background: linear-gradient(135deg, #D32F2F 0%, #B71C1C 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    box-shadow: 0 2px 8px rgba(211, 47, 47, 0.3);
}

.avatar-container .avatar-content {
    width: 1.25rem;
    height: 1.25rem;
    background-color: white;
    border-radius: 0.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.avatar-container .avatar-text {
    color: #D32F2F;
    font-weight: bold;
    font-size: 0.75rem;
    letter-spacing: 0.3px;
}

/* Enhanced Header Styling */
.widget-header {
    background: linear-gradient(135deg, #D32F2F 0%, #B71C1C 100%);
    color: white;
    padding: 2rem 1.5rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    min-height: 140px;
}

.chat-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-bottom: 1px solid #e1e8ed;
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 60px;
    max-height: 60px;
}

/* Enhanced Chat Area */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 0 1.5rem 1.5rem 1.5rem;
    background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);
    min-height: 0;
    max-height: calc(100vh - 200px);
    display: flex;
    flex-direction: column;
    gap: 1rem;
    justify-content: flex-start;
}

/* Ensure both welcome and chat screens have consistent height */
#welcome-screen,
#chat-screen {
    height: 100%;
    max-height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* Ensure welcome screen content area matches chat area */
.laravel-card-body {
    flex: 1;
    overflow-y: auto;
}

.chat-input-area {
    padding: 1.25rem 1.5rem;
    background: white;
    border-top: 1px solid #e1e8ed;
}

/* Enhanced Avatar Styles */
.chat-avatar {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.chat-avatar:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.chat-avatar-user {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: 2px solid #ffffff;
}

.chat-avatar-assistant {
    background: linear-gradient(135deg, #D32F2F 0%, #B71C1C 100%);
    border: 2px solid #ffffff;
}

.chat-avatar-inner {
    width: 1.25rem;
    height: 1.25rem;
    background-color: white;
    border-radius: 0.125rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.75rem;
    color: #D32F2F;
}

/* Prevent excessive spacing in chat */
.chat-messages:empty {
    min-height: 0 !important;
    padding: 0 !important;
    display: none;
}

.chat-messages > div {
    margin-bottom: 0.75rem;
}

.chat-messages > div:first-child {
    margin-top: 0;
}

.chat-messages > div:last-child {
    margin-bottom: 0;
}

/* Ensure proper flex behavior */
#messages-container {
    flex-grow: 1;
    flex-shrink: 1;
    flex-basis: 0;
    height: 100%;
}

/* Reduce top spacing on mobile */
@media (max-width: 480px) {
    .chat-messages {
        padding: 0 1rem 1rem 1rem;
        min-height: 0;
        max-height: calc(100% - 50px);
    }

    .chat-messages:empty {
        min-height: 0 !important;
        padding: 0 !important;
        display: none;
    }

    .chat-messages > div:first-child {
        margin-top: 0;
    }

    .chat-messages {
        max-height: calc(70vh - 150px) !important;
    }

    #welcome-screen,
    #chat-screen {
        height: 100% !important;
        max-height: 100% !important;
    }

    .chat-header {
        padding: 0.5rem 0.75rem;
        min-height: 50px;
        max-height: 50px;
    }

    .avatar-container {
        width: 2rem;
        height: 2rem;
    }

    .avatar-container .avatar-content {
        width: 1.1rem;
        height: 1.1rem;
    }

    .avatar-container .avatar-text {
        font-size: 0.7rem;
    }
}

/* Reduce top spacing on tablet */
@media (min-width: 481px) and (max-width: 768px) {
    .chat-messages {
        padding: 0 1.25rem 1.25rem 1.25rem;
        min-height: 0;
        max-height: calc(100% - 55px);
    }

    .chat-messages:empty {
        min-height: 0 !important;
        padding: 0 !important;
        display: none;
    }

    .chat-messages > div:first-child {
        margin-top: 0;
    }

    .chat-messages {
        max-height: calc(600px - 180px) !important;
    }

    #welcome-screen,
    #chat-screen {
        height: 100% !important;
        max-height: 100% !important;
    }

    .chat-header {
        padding: 0.625rem 0.875rem;
        min-height: 55px;
        max-height: 55px;
    }

    .avatar-container {
        width: 2.125rem;
        height: 2.125rem;
    }

    .avatar-container .avatar-content {
        width: 1.175rem;
        height: 1.175rem;
    }

    .avatar-container .avatar-text {
        font-size: 0.725rem;
    }
}

/* Enhanced Animation classes */
.fade-in {
    animation: fadeIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-up {
    animation: slideUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.bounce-dot {
    animation: bounce 1.4s ease-in-out infinite both;
    width: 0.5rem;
    height: 0.5rem;
    background: #D32F2F;
    border-radius: 50%;
}

.bounce-dot:nth-child(1) { animation-delay: -0.32s; }
.bounce-dot:nth-child(2) { animation-delay: -0.16s; }

/* Enhanced Blinking eye animation for chatbot trigger */
.chatbot-eye {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chatbot-eye-blink {
    animation: eyeBlink 0.4s ease-in-out;
}

.chatbot-trigger-loaded {
    animation: initialBlink 4s ease-in-out;
}

.chatbot-face {
    transition: all 0.3s ease;
}

.chatbot-face:hover {
    transform: scale(1.05);
}

.chatbot-face:hover .chatbot-eye {
    transform: scaleY(0.7);
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px) rotateX(-10deg) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) rotateX(0) scale(1);
    }
}

@keyframes bounce {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

@keyframes eyeBlink {
    0%, 100% { transform: scaleY(1); }
    50% { transform: scaleY(0.2); }
}

@keyframes initialBlink {
    0%, 8%, 16%, 24%, 32%, 40% { transform: scaleY(1); }
    4%, 12%, 20%, 28% { transform: scaleY(0.2); }
    50%, 100% { transform: scaleY(1); }
}

/* Widget positioning - Enhanced responsive positioning */
.widget-container {
    position: fixed;
    bottom: calc(1.5rem + 22px); /* Move 2.3cm (87px) upward from previous position */
    right: calc(6.5rem + 243px); /* Move 4 cm more to the left (152px additional total) */
    z-index: 9998; /* One level below back to top button */
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.widget-container:hover {
    transform: translateY(-2px);
}

/* Responsive chatbot trigger button sizing */
#widget-trigger button {
    width: 64px !important;
    height: 64px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Large screens (1200px+) - Larger button */
@media (min-width: 1200px) {
    #widget-trigger button {
        width: 68px !important;
        height: 68px !important;
    }

    .widget-container {
        right: calc(6.5rem + 85px); /* Slightly more offset for large screens */
    }
}

/* Medium-large screens (992px - 1199px) */
@media (min-width: 992px) and (max-width: 1199px) {
    #widget-trigger button {
        width: 60px !important;
        height: 60px !important;
    }
}

/* Medium screens (769px - 991px) */
@media (min-width: 769px) and (max-width: 991px) {
    .widget-container {
        right: calc(4rem + 60px); /* Adjusted for medium screens */
    }

    #widget-trigger button {
        width: 56px !important;
        height: 56px !important;
    }
}

/* Responsive design */
/* Mobile devices (up to 480px) */
@media (max-width: 480px) {
    .widget-container {
        bottom: calc(1rem - 27px); /* Move 2.3cm (87px) upward from previous mobile position */
        right: calc(1rem + 330px); /* Move 8.7cm (330px) to the right for mobile - 4cm more */
        left: auto;
    }

    #widget-trigger button {
        width: 48px !important;
        height: 48px !important;
    }
}

    .widget-main {
        width: calc(100vw - 2rem) !important;
        max-height: 70vh !important;
        border-radius: 1rem !important;
    }

    .widget-header {
        padding: 1.5rem 1rem !important;
        min-height: 100px !important;
    }

    .widget-header h2 {
        font-size: 1.25rem !important;
    }

    .chat-messages {
        padding: 1rem !important;
        min-height: 120px !important;
        max-height: calc(100% - 140px) !important;
        gap: 0.75rem !important;
    }

    .chat-input-area {
        padding: 1rem !important;
    }

    /* Mobile Avatar Styles */
    .chat-avatar {
        width: 1.75rem !important;
        height: 1.75rem !important;
    }

    /* Modern Welcome Screen Mobile Styles */
    .welcome-header-gradient {
        padding: 20px 16px 28px 16px;
    }

    .welcome-title-modern {
        font-size: 20px;
    }

    .wave-emoji {
        font-size: 28px;
    }

    .welcome-content-modern {
        padding: 20px 16px 16px 16px;
        gap: 16px;
    }

    .chatbot-info-card {
        padding: 12px;
    }

    .chatbot-avatar-modern {
        width: 36px;
        height: 36px;
    }

    .chatbot-name {
        font-size: 15px;
    }

    .chatbot-subtitle {
        font-size: 13px;
    }

    .chat-btn-modern {
        padding: 14px 20px;
        font-size: 15px;
    }

    .action-item {
        padding: 12px 14px;
    }

    .action-text {
        font-size: 13px;
    }

    .nav-item-modern {
        padding: 10px 6px;
        font-size: 11px;
    }

    /* Reference Chat Mobile Styles */
    .reference-chat-header {
        padding: 10px 12px;
        min-height: 52px;
    }

    .ai-assistant-badge {
        padding: 4px 8px;
    }

    .ai-avatar-circle {
        width: 20px;
        height: 20px;
    }

    .ai-assistant-text {
        font-size: 13px;
    }

    .reference-messages-area {
        padding: 16px 12px;
        gap: 12px;
    }

    .message-text-ref,
    .assistant-message-ref {
        max-width: calc(100% - 60px);
        padding: 10px 14px;
        font-size: 13px;
    }

    .assistant-message-ref {
        margin-left: 36px;
    }

    .message-avatar-ref {
        width: 28px;
        height: 28px;
    }

    .action-buttons-grid-ref {
        margin-left: 36px;
        max-width: calc(100% - 60px);
    }

    .action-btn-ref {
        padding: 6px 12px;
        font-size: 12px;
    }

    .reference-input-area {
        padding: 12px;
    }

    .input-wrapper-ref {
        padding: 6px 10px;
    }

    .message-input-ref {
        font-size: 13px;
        padding: 6px 4px;
    }

    .emoji-btn-ref,
    .attach-btn-ref,
    .send-btn-ref {
        padding: 6px;
    }
}

/* Extra small screens (up to 360px) */
@media (max-width: 360px) {
    #widget-trigger button {
        width: 44px !important;
        height: 44px !important;
    }

    .widget-container {
        bottom: calc(0.8rem + 60px); /* Move 2.3cm (87px) upward from base position */
        right: calc(0.8rem + 146px); /* Move a little bit more to the right (26px additional) */
    }

    /* Reference Chat Extra Small Screen Styles */
    .reference-chat-header {
        padding: 8px 10px;
        min-height: 48px;
    }

    .ai-assistant-text {
        font-size: 12px;
    }

    .message-text-ref,
    .assistant-message-ref {
        max-width: calc(100% - 50px);
        padding: 8px 12px;
        font-size: 12px;
    }

    .assistant-message-ref {
        margin-left: 32px;
    }

    .message-avatar-ref {
        width: 24px;
        height: 24px;
    }

    .action-buttons-grid-ref {
        margin-left: 32px;
        max-width: calc(100% - 50px);
    }

    .action-btn-ref {
        padding: 5px 10px;
        font-size: 11px;
    }

    .reference-input-area {
        padding: 10px;
    }

    .input-wrapper-ref {
        padding: 5px 8px;
    }

    .message-input-ref {
        font-size: 12px;
        padding: 5px 4px;
    }
}

    .chat-avatar-inner {
        width: 1rem !important;
        height: 1rem !important;
        font-size: 0.625rem !important;
    }

    .laravel-card-body {
        padding: 1rem 1.5rem !important;
    }

    /* Emoji picker responsive */
    #emoji-picker {
        width: calc(100vw - 4rem) !important;
        max-width: 280px !important;
        right: 1rem !important;
        bottom: 4rem !important;
    }

    #emoji-grid {
        grid-template-columns: repeat(6, 1fr) !important;
    }

    /* Touch targets */
    .chat-button {
        min-width: 44px !important;
        min-height: 44px !important;
        padding: 0.75rem !important;
    }

    /* Font size adjustments */
    .avatar-text {
        font-size: 0.875rem !important;
    }

    .laravel-btn {
        padding: 0.875rem 1rem !important;
        font-size: 0.875rem !important;
    }
}

/* Tablet devices (481px to 768px) */
@media (min-width: 481px) and (max-width: 768px) {
    .widget-container {
        bottom: 1.5rem;
        right: calc(1.5rem + 100px); /* Original position + 2.6cm (100px) more to the right */
    }

    #widget-trigger button {
        width: 52px !important;
        height: 52px !important;
    }
}

    .widget-main {
        width: 350px !important;
        max-height: 600px !important;
        border-radius: 1.125rem !important;
    }

    .widget-header {
        padding: 1.75rem 1.25rem !important;
        min-height: 120px !important;
    }

    .chat-messages {
        padding: 1.25rem !important;
        min-height: 180px !important;
        max-height: calc(100% - 160px) !important;
        gap: 0.875rem !important;
    }

    .chat-input-area {
        padding: 1.125rem 1.25rem !important;
    }

    /* Tablet Avatar Styles */
    .chat-avatar {
        width: 1.875rem !important;
        height: 1.875rem !important;
    }

    .chat-avatar-inner {
        width: 1.125rem !important;
        height: 1.125rem !important;
        font-size: 0.6875rem !important;
    }

    /* Emoji picker for tablet */
    #emoji-picker {
        width: 300px !important;
        right: 1.25rem !important;
        bottom: 4.5rem !important;
    }

    #emoji-grid {
        grid-template-columns: repeat(7, 1fr) !important;
    }
}

/* Small tablets and large phones (641px to 768px) - legacy support */
@media (min-width: 641px) and (max-width: 768px) {
    .chat-widget-main {
        width: 350px !important;
        height: 550px !important;
    }
}

/* Legacy mobile support (up to 640px) */
@media (max-width: 640px) {
    .chat-widget-main {
        width: calc(100vw - 2rem) !important;
        height: 70vh !important;
        max-height: 500px !important;
    }
}

/* Laravel specific utilities */
.laravel-btn {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border: 1px solid transparent;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.375rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    color: white;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
}

.laravel-btn-primary {
    background: linear-gradient(135deg, #D32F2F 0%, #B71C1C 100%);
    border-color: #D32F2F;
    box-shadow: 0 4px 12px rgba(211, 47, 47, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.laravel-btn-primary:hover {
    background: linear-gradient(135deg, #B71C1C 0%, #8D1515 100%);
    border-color: #B71C1C;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(211, 47, 47, 0.4);
}

.laravel-btn-primary:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(211, 47, 47, 0.3);
}

.laravel-btn-secondary {
    background-color: #6b7280;
    border-color: #6b7280;
}

.laravel-btn-secondary:hover {
    background-color: #4b5563;
    border-color: #4b5563;
}

.laravel-btn-secondary:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(107, 114, 128, 0.5);
}

.laravel-input {
    display: block;
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    font-size: 0.875rem;
    line-height: 1.25rem;
}

.laravel-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.laravel-card {
    background-color: white;
    overflow: hidden;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    border-radius: 0.5rem;
}

.laravel-card-body {
    padding: 1rem 1.5rem;
}

/* Enhanced Custom Icons using CSS */
.icon-send {
    width: 18px;
    height: 18px;
    background: currentColor;
    mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M3.4 20.4l17.45-7.48c.81-.35.81-1.49 0-1.84L3.4 3.6c-.66-.29-1.39.2-1.39.95L2 9.12c0 .5.37.93.87.99L17 12 2.87 13.88c-.5.07-.87.49-.87.99l.01 4.57c0 .75.73 1.24 1.39.95z'/%3E%3C/svg%3E") no-repeat center;
    mask-size: contain;
}

.icon-emoji {
    width: 18px;
    height: 18px;
    background: currentColor;
    mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM8.5 9c.83 0 1.5.67 1.5 1.5S9.33 12 8.5 12 7 11.33 7 10.5 7.67 9 8.5 9zm7 0c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5-1.5-.67-1.5-1.5S14.67 9 15.5 9zm-3.5 6.5c2.33 0 4.31-1.46 5.11-3.5H6.89c.8 2.04 2.78 3.5 5.11 3.5z'/%3E%3C/svg%3E") no-repeat center;
    mask-size: contain;
}

.icon-close {
    width: 16px;
    height: 16px;
    background: currentColor;
    mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M18.3 5.71c-.39-.39-1.02-.39-1.41 0L12 10.59 7.11 5.7c-.39-.39-1.02-.39-1.41 0-.39.39-.39 1.02 0 1.41L10.59 12 5.7 16.89c-.39.39-.39 1.02 0 1.41.39.39 1.02.39 1.41 0L12 13.41l4.89 4.89c.39.39 1.02.39 1.41 0 .39-.39.39-1.02 0-1.41L13.41 12l4.89-4.89c.38-.38.38-1.02 0-1.4z'/%3E%3C/svg%3E") no-repeat center;
    mask-size: contain;
}

.icon-minimize {
    width: 16px;
    height: 16px;
    background: currentColor;
    mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M6 19h12c.55 0 1-.45 1-1s-.45-1-1-1H6c-.55 0-1 .45-1 1s.45 1 1 1z'/%3E%3C/svg%3E") no-repeat center;
    mask-size: contain;
}

.icon-new-chat {
    width: 16px;
    height: 16px;
    background: currentColor;
    mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm4 11h-3v3c0 .55-.45 1-1 1s-1-.45-1-1v-3H8c-.55 0-1-.45-1-1s.45-1 1-1h3V8c0-.55.45-1 1-1s1 .45 1 1v3h3c.55 0 1 .45 1 1s-.45 1-1 1z'/%3E%3C/svg%3E") no-repeat center;
    mask-size: contain;
}

.icon-rephrase {
    width: 16px;
    height: 16px;
    background: currentColor;
    mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12 6v3l4-4-4-4v3c-4.42 0-8 3.58-8 8 0 1.57.46 3.03 1.24 4.26L6.7 14.8c-.45-.83-.7-1.79-.7-2.8 0-3.31 2.69-6 6-6zm6.76 1.74L17.3 9.2c.44.84.7 1.79.7 2.8 0 3.31-2.69 6-6 6v-3l-4 4 4 4v-3c4.42 0 8-3.58 8-8 0-1.57-.46-3.03-1.24-4.26z'/%3E%3C/svg%3E") no-repeat center;
    mask-size: contain;
}

/* Enhanced Avatar Icons */
.avatar-icon {
    width: 20px;
    height: 20px;
    background: currentColor;
    mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L9 7V9C9 10.1 9.9 11 11 11V14L13 16L15 14V11C16.1 11 17 10.1 17 9Z'/%3E%3C/svg%3E") no-repeat center;
    mask-size: contain;
}

.chat-icon {
    width: 22px;
    height: 22px;
    background: currentColor;
    mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-3 12H7c-.55 0-1-.45-1-1s.45-1 1-1h10c.55 0 1 .45 1 1s-.45 1-1 1zm0-3H7c-.55 0-1-.45-1-1s.45-1 1-1h10c.55 0 1 .45 1 1s-.45 1-1 1zm0-3H7c-.55 0-1-.45-1-1s.45-1 1-1h10c.55 0 1 .45 1 1s-.45 1-1 1z'/%3E%3C/svg%3E") no-repeat center;
    mask-size: contain;
}

/* Enhanced chat input styling */
.chat-input {
    border-radius: 25px;
    border: 2px solid #e1e8ed;
    background: #ffffff;
    padding: 0.75rem 1rem;
    padding-right: 3rem;
    width: 100%;
    font-size: 0.875rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.chat-input:focus {
    outline: none;
    border-color: #D32F2F;
    box-shadow: 0 0 0 3px rgba(211, 47, 47, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

/* Enhanced button styling */
.chat-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
}

.chat-button:hover {
    background-color: rgba(211, 47, 47, 0.1);
    transform: scale(1.1);
}

.chat-button:active {
    transform: scale(0.95);
}
</style>
@endif
<link href="{{ asset('assets/css/toastr.min.css') }}" rel="stylesheet" />